{"version": 3, "model_type": "link_invent", "run_type": "reinforcement_learning", "logging": {"sender": "", "recipient": "local", "logging_path": "results-BRD4/results", "result_folder": "results-BRD4/logs", "job_name": "Link-INVENT RL Demo", "job_id": "N/A"}, "parameters": {"actor": "linkinvent.prior", "critic": "linkinvent.prior", "warheads": ["NC(=O)c1c(N)n(nc1-c1ccc(Oc2ccc(F)cc2F)cc1)[C@@H]1CCCN(C1)[100C](*)=O|CN[C@@H](C)C(=O)N[C@H](C(=O)N1Cc2cc([101O]*)ccc2C[C@H]1C(=O)N[C@@H]1CCCc2ccccc12)C(C)(C)C"], "n_steps": 2, "learning_rate": 0.0001, "batch_size": 128, "randomize_warheads": true, "learning_strategy": {"name": "dap", "parameters": {"sigma": 120}}, "scoring_strategy": {"name": "link_invent", "diversity_filter": {"bucket_size": 20, "minscore": 0.4, "minsimilarity": 0.4, "name": "NoFilterWithPenalty"}, "scoring_function": {"name": "custom_product", "parallel": false, "parameters": [{"weight": 1, "component_type": "linker_num_hbd", "name": "Linker Num HBD", "specific_parameters": {"transformation": {"high": 6, "low": 0, "transformation_type": "reverse_sigmoid", "k": 0.15}}}, {"weight": 1, "component_type": "linker_num_rings", "name": "<PERSON><PERSON>", "specific_parameters": {"transformation": {"high": 1, "low": 1, "transformation_type": "step"}}}]}}}}